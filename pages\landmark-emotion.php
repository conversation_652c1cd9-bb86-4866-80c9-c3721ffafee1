<?php
/**
 * MoodifyMe - Landmark-Based Emotion Detection Page
 * Advanced facial emotion detection using MediaPipe landmarks
 */

require_once '../config.php';
require_once '../includes/auth_check.php';

$pageTitle = 'Landmark Emotion Detection';
$currentPage = 'landmark-emotion.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - MoodifyMe</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .landmark-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        #landmark-video {
            width: 100%;
            height: auto;
            max-height: 480px;
            object-fit: cover;
        }

        #landmark-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 10;
        }

        .emotion-display {
            background: linear-gradient(135deg, #E55100 0%, #D32F2F 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(229, 81, 0, 0.3);
        }

        .emotion-badge {
            display: inline-block;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            margin: 10px;
            backdrop-filter: blur(10px);
        }

        .confidence-bar {
            background: rgba(255, 255, 255, 0.3);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .confidence-fill {
            background: #fff;
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn-landmark {
            background: linear-gradient(135deg, #E55100 0%, #D32F2F 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(229, 81, 0, 0.3);
        }

        .btn-landmark:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(229, 81, 0, 0.4);
            color: white;
        }

        .status-indicator {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
        }

        .status-loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .landmark-points {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 15;
        }

        .landmark-point {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ff00;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        .emotion-history {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }

        .emotion-history h5 {
            color: #E55100;
            margin-bottom: 15px;
        }

        .history-item {
            display: inline-block;
            background: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 2px;
            font-size: 0.9em;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid landmark-container">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1><i class="fas fa-brain me-2"></i> Advanced Landmark Emotion Detection</h1>
                    <p class="lead">Real-time emotion analysis using 468 facial landmarks</p>
                </div>

                <!-- Status Indicator -->
                <div id="status-indicator" class="status-indicator status-loading">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Initializing MediaPipe Face Mesh...
                </div>

                <!-- Video Container -->
                <div class="video-container">
                    <video id="landmark-video" autoplay muted playsinline></video>
                    <canvas id="landmark-canvas"></canvas>
                    <div id="landmark-points" class="landmark-points"></div>
                </div>

                <!-- Controls -->
                <div class="controls">
                    <button id="start-detection" class="btn btn-landmark" disabled>
                        <i class="fas fa-play me-2"></i> Start Detection
                    </button>
                    <button id="stop-detection" class="btn btn-landmark" disabled>
                        <i class="fas fa-stop me-2"></i> Stop Detection
                    </button>
                    <button id="capture-emotion" class="btn btn-landmark" disabled>
                        <i class="fas fa-camera me-2"></i> Capture Emotion
                    </button>
                    <button id="reset-history" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-2"></i> Reset History
                    </button>
                </div>

                <!-- Current Emotion Display -->
                <div id="emotion-display" class="emotion-display" style="display: none;">
                    <h3><i class="fas fa-heart me-2"></i> Current Emotion</h3>
                    <div class="emotion-badge">
                        <i id="emotion-icon" class="fas fa-meh"></i>
                        <span id="emotion-name">Neutral</span>
                    </div>
                    <div class="confidence-bar">
                        <div id="confidence-fill" class="confidence-fill" style="width: 0%"></div>
                    </div>
                    <small id="confidence-text">0% confidence</small>
                </div>

                <!-- Emotion History -->
                <div id="emotion-history" class="emotion-history" style="display: none;">
                    <h5><i class="fas fa-history me-2"></i> Emotion History</h5>
                    <div id="history-items"></div>
                </div>

                <!-- Action Buttons -->
                <div id="action-buttons" class="text-center mt-4" style="display: none;">
                    <h4>What would you like to do with your detected emotion?</h4>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <button class="btn btn-outline-primary w-100" onclick="proceedToMoodOptions()">
                                <i class="fas fa-arrow-right me-2"></i> Continue to Recommendations
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100" onclick="tryAgain()">
                                <i class="fas fa-redo me-2"></i> Try Again
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info w-100" onclick="viewAnalytics()">
                                <i class="fas fa-chart-line me-2"></i> View Analytics
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-warning w-100" onclick="goHome()">
                                <i class="fas fa-home me-2"></i> Back to Home
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <!-- MediaPipe Scripts -->
    <script src="../assets/js/mediapipe/camera_utils.js"></script>
    <script src="../assets/js/mediapipe/drawing_utils.js"></script>
    <script src="../assets/js/mediapipe/face_mesh.js"></script>

    <!-- Custom Scripts -->
    <script src="../assets/js/landmark-emotion-detector.js"></script>

    <script>
        // Global variables
        let emotionDetector = null;
        let currentEmotion = null;
        let isDetecting = false;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeLandmarkDetection();
        });

        async function initializeLandmarkDetection() {
            try {
                updateStatus('Initializing MediaPipe Face Mesh...', 'loading');

                // Debug: Check if required classes are available
                console.log('Checking dependencies...');
                console.log('LandmarkEmotionDetector available:', typeof LandmarkEmotionDetector);
                console.log('FaceMesh available:', typeof FaceMesh);
                console.log('Camera available:', typeof Camera);

                // Check if LandmarkEmotionDetector is defined
                if (typeof LandmarkEmotionDetector === 'undefined') {
                    throw new Error('LandmarkEmotionDetector class is not defined. Check if landmark-emotion-detector.js loaded correctly.');
                }

                // Create emotion detector
                emotionDetector = new LandmarkEmotionDetector();

                // Set up callbacks
                emotionDetector.setCallbacks({
                    onInitialized: () => {
                        updateStatus('MediaPipe initialized successfully!', 'ready');
                        document.getElementById('start-detection').disabled = false;
                    },
                    onEmotionDetected: (result) => {
                        updateEmotionDisplay(result);
                        addToHistory(result);
                    },
                    onError: (error) => {
                        updateStatus(`Error: ${error.message}`, 'error');
                        console.error('Landmark detection error:', error);
                    }
                });

                // Initialize
                await emotionDetector.initialize();

            } catch (error) {
                updateStatus(`Initialization failed: ${error.message}`, 'error');
                console.error('Failed to initialize landmark detection:', error);
            }
        }

        // Start detection
        document.getElementById('start-detection').addEventListener('click', async function() {
            try {
                const video = document.getElementById('landmark-video');
                await emotionDetector.startDetection(video);

                isDetecting = true;
                this.disabled = true;
                document.getElementById('stop-detection').disabled = false;
                document.getElementById('capture-emotion').disabled = false;

                updateStatus('Detection started - analyzing your emotions...', 'ready');
                document.getElementById('emotion-display').style.display = 'block';

            } catch (error) {
                updateStatus(`Failed to start detection: ${error.message}`, 'error');
            }
        });

        // Stop detection
        document.getElementById('stop-detection').addEventListener('click', function() {
            emotionDetector.stopDetection();

            isDetecting = false;
            document.getElementById('start-detection').disabled = false;
            this.disabled = true;
            document.getElementById('capture-emotion').disabled = true;

            updateStatus('Detection stopped', 'ready');
        });

        // Capture current emotion
        document.getElementById('capture-emotion').addEventListener('click', function() {
            const emotion = emotionDetector.getCurrentEmotion();
            if (emotion.emotion && emotion.confidence > 0.3) {
                currentEmotion = emotion;
                document.getElementById('action-buttons').style.display = 'block';
                updateStatus(`Captured emotion: ${emotion.emotion} (${Math.round(emotion.confidence * 100)}% confidence)`, 'ready');
            } else {
                updateStatus('No clear emotion detected. Please try again.', 'error');
            }
        });

        // Reset history
        document.getElementById('reset-history').addEventListener('click', function() {
            emotionDetector.resetHistory();
            document.getElementById('history-items').innerHTML = '';
            document.getElementById('emotion-history').style.display = 'none';
            updateStatus('Emotion history cleared', 'ready');
        });

        function updateStatus(message, type) {
            const indicator = document.getElementById('status-indicator');
            indicator.className = `status-indicator status-${type}`;

            let icon = 'fas fa-info-circle';
            if (type === 'loading') icon = 'fas fa-spinner fa-spin';
            else if (type === 'ready') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-triangle';

            indicator.innerHTML = `<i class="${icon} me-2"></i>${message}`;
        }

        function updateEmotionDisplay(result) {
            const emotionName = document.getElementById('emotion-name');
            const emotionIcon = document.getElementById('emotion-icon');
            const confidenceFill = document.getElementById('confidence-fill');
            const confidenceText = document.getElementById('confidence-text');

            emotionName.textContent = result.emotion.charAt(0).toUpperCase() + result.emotion.slice(1);
            emotionIcon.className = `fas fa-${getEmotionIcon(result.emotion)}`;

            const confidencePercent = Math.round(result.confidence * 100);
            confidenceFill.style.width = `${confidencePercent}%`;
            confidenceText.textContent = `${confidencePercent}% confidence`;
        }

        function addToHistory(result) {
            const historyContainer = document.getElementById('emotion-history');
            const historyItems = document.getElementById('history-items');

            const item = document.createElement('span');
            item.className = 'history-item';
            item.innerHTML = `${result.emotion} (${Math.round(result.confidence * 100)}%)`;

            historyItems.appendChild(item);
            historyContainer.style.display = 'block';

            // Keep only last 20 items
            const items = historyItems.children;
            if (items.length > 20) {
                historyItems.removeChild(items[0]);
            }
        }

        function getEmotionIcon(emotion) {
            const icons = {
                'happy': 'smile',
                'sad': 'frown',
                'angry': 'angry',
                'surprised': 'surprise',
                'fear': 'tired',
                'disgust': 'grimace',
                'neutral': 'meh'
            };
            return icons[emotion] || 'meh';
        }

        // Action button functions
        function proceedToMoodOptions() {
            if (currentEmotion) {
                const params = new URLSearchParams({
                    source: currentEmotion.emotion,
                    target: 'happy', // Default target
                    confidence: currentEmotion.confidence,
                    method: 'landmark_detection'
                });
                window.location.href = `mood_options.php?${params.toString()}`;
            }
        }

        function tryAgain() {
            document.getElementById('action-buttons').style.display = 'none';
            currentEmotion = null;
            updateStatus('Ready to detect emotions again', 'ready');
        }

        function viewAnalytics() {
            window.location.href = 'dashboard.php';
        }

        function goHome() {
            window.location.href = '../index.php';
        }
    </script>
</body>
</html>
